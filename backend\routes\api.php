<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\GalaxyController;
use App\Http\Controllers\Api\ImageController;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// Galaxy API routes
Route::apiResource('galaxies', GalaxyController::class);

// Demo route for testing
Route::get('/galaxies/demo/1', function () {
    return response()->json([
        'success' => true,
        'data' => [
            'id' => 'demo',
            'messages' => [
                "I love you so much! ❤️",
                "Our Anniversary",
                "I love you 💖",
                "25/08/2004",
                "Thank you for being my sunshine",
                "Thank you for being my everything 💕",
                "You are my universe",
                "There is no other",
                "You're amazing",
                "You make my heart smile",
                "Love ya! 💖",
                "Honey bunch, you are my everything!"
            ],
            'icons' => ["♥", "💖", "❤️", "❤️", "💕", "💕"],
            'colors' => [
                'love' => "#ff6b9d",
                'birthday' => "#4ecdc4",
                'date' => "#ff69b4",
                'special' => "#ff6b9d",
                'heart' => "#ff69b4"
            ],
            'song' => "eyenoselip.mp3",
            'images' => [],
            'created_at' => "2025-05-30T00:00:00.000Z"
        ]
    ]);
});

// Image upload routes
Route::post('/images/upload', [ImageController::class, 'upload']);
Route::delete('/images', [ImageController::class, 'delete']);
