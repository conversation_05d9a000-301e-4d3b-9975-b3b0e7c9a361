<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('galaxies', function (Blueprint $table) {
            $table->id();
            $table->json('messages'); // Array of love messages
            $table->json('icons'); // Array of icons/emojis
            $table->json('colors'); // Object with color themes
            $table->string('song')->nullable(); // Song filename or URL
            $table->json('images')->nullable(); // Array of image URLs
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('galaxies');
    }
};
