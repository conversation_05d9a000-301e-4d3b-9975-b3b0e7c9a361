# Love Galaxy - PHP/Laravel Migration

This project has been migrated from Firebase to PHP/Laravel with MySQL database.

## Architecture Overview

### Backend (PHP/Laravel)
- **Framework**: <PERSON><PERSON> 12
- **Database**: MySQL
- **API**: RESTful API endpoints
- **File Storage**: Local storage (replaceable with cloud storage)

### Frontend
- **Technology**: Vanilla JavaScript with HTML/CSS
- **API Integration**: Custom API client replacing Firebase SDK

## Setup Instructions

### Prerequisites
- PHP 8.1+
- Composer
- MySQL 8.0+
- Node.js (optional, for frontend development)

### Backend Setup

1. **Navigate to backend directory**
   ```bash
   cd backend
   ```

2. **Install dependencies**
   ```bash
   composer install
   ```

3. **Configure environment**
   ```bash
   cp .env.example .env
   ```
   
   Update `.env` file with your MySQL database credentials:
   ```
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=love_galaxy
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

4. **Generate application key**
   ```bash
   php artisan key:generate
   ```

5. **Create database**
   Create a MySQL database named `love_galaxy`

6. **Run migrations**
   ```bash
   php artisan migrate
   ```

7. **Seed demo data (optional)**
   ```bash
   php artisan db:seed --class=GalaxySeeder
   ```

8. **Create storage link**
   ```bash
   php artisan storage:link
   ```

9. **Start development server**
   ```bash
   php artisan serve
   ```

The application will be available at `http://localhost:8000`

## API Endpoints

### Galaxies
- `GET /api/galaxies` - List all galaxies
- `POST /api/galaxies` - Create new galaxy
- `GET /api/galaxies/{id}` - Get specific galaxy
- `PUT /api/galaxies/{id}` - Update galaxy
- `DELETE /api/galaxies/{id}` - Delete galaxy
- `GET /api/galaxies/demo/1` - Get demo galaxy data

### Images
- `POST /api/images/upload` - Upload images
- `DELETE /api/images` - Delete image

## Database Schema

### Galaxies Table
- `id` - Primary key
- `messages` - JSON array of love messages
- `icons` - JSON array of icons/emojis
- `colors` - JSON object with color themes
- `song` - Song filename or URL (nullable)
- `images` - JSON array of image URLs (nullable)
- `created_at` - Timestamp
- `updated_at` - Timestamp

## Migration Notes

### Changes Made
1. **Database**: Replaced Firebase Firestore with MySQL
2. **Backend**: Created Laravel API with proper validation and error handling
3. **File Storage**: Replaced Firebase Storage with Laravel's local storage
4. **API Client**: Created custom JavaScript API client to replace Firebase SDK
5. **Security**: Implemented proper validation, CORS, and security measures

### Frontend Compatibility
- All existing frontend functionality is preserved
- UI/UX remains unchanged
- Demo functionality works without database

### Security Features
- Input validation on all API endpoints
- File upload restrictions (type, size)
- CORS configuration
- Error handling and logging

## Development

### Adding New Features
1. Create new migrations for database changes
2. Update models with new fields
3. Add validation rules in controllers
4. Update API client if needed
5. Test thoroughly

### Testing
Run the application and test:
1. Demo functionality: `http://localhost:8000/galaxy-viewer.html?demo=1`
2. Create new galaxy: `http://localhost:8000/creator.html`
3. API endpoints: Use Postman or similar tools

## Production Deployment

1. Configure production database
2. Set up proper file storage (AWS S3, etc.)
3. Configure environment variables
4. Set up SSL certificates
5. Configure web server (Apache/Nginx)
6. Set up monitoring and logging

## Support

For issues or questions, please check the Laravel documentation or create an issue in the project repository.
