<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Galaxy;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;

class GalaxyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $galaxies = Galaxy::latest()->paginate(10);
        return response()->json($galaxies);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'messages' => 'required|array|min:1',
            'messages.*' => 'required|string|max:500',
            'icons' => 'required|array|min:1',
            'icons.*' => 'required|string|max:10',
            'colors' => 'required|array',
            'colors.love' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'colors.birthday' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'colors.date' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'colors.special' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'colors.heart' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'song' => 'nullable|string|max:255',
            'images' => 'nullable|array|max:5',
            'images.*' => 'nullable|string|url'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        try {
            $galaxy = Galaxy::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Galaxy created successfully',
                'data' => $galaxy
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to create galaxy',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $galaxy = Galaxy::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $galaxy
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Galaxy not found',
                'message' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $galaxy = Galaxy::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'messages' => 'sometimes|array|min:1',
                'messages.*' => 'required|string|max:500',
                'icons' => 'sometimes|array|min:1',
                'icons.*' => 'required|string|max:10',
                'colors' => 'sometimes|array',
                'song' => 'nullable|string|max:255',
                'images' => 'nullable|array|max:5',
                'images.*' => 'nullable|string|url'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'error' => 'Validation failed',
                    'messages' => $validator->errors()
                ], 422);
            }

            $galaxy->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Galaxy updated successfully',
                'data' => $galaxy
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update galaxy',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $galaxy = Galaxy::findOrFail($id);
            $galaxy->delete();

            return response()->json([
                'success' => true,
                'message' => 'Galaxy deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to delete galaxy',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
