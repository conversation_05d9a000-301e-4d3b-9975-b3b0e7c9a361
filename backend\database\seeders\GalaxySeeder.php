<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Galaxy;

class GalaxySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Galaxy::create([
            'messages' => [
                "I love you so much! ❤️",
                "Our Anniversary",
                "I love you 💖",
                "25/08/2004",
                "Thank you for being my sunshine",
                "Thank you for being my everything 💕",
                "You are my universe",
                "There is no other",
                "You're amazing",
                "You make my heart smile",
                "Love ya! 💖",
                "Honey bunch, you are my everything!"
            ],
            'icons' => ["♥", "💖", "❤️", "❤️", "💕", "💕"],
            'colors' => [
                'love' => "#ff6b9d",
                'birthday' => "#4ecdc4",
                'date' => "#ff69b4",
                'special' => "#ff6b9d",
                'heart' => "#ff69b4"
            ],
            'song' => "eyenoselip.mp3",
            'images' => []
        ]);
    }
}
