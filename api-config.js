// API Configuration for PHP Laravel Backend
const API_BASE_URL = 'http://localhost:8000/api';

// API Client class to handle all HTTP requests
class ApiClient {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
    }

    async request(endpoint, options = {}) {
        const url = `${this.baseUrl}${endpoint}`;
        const config = {
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        };

        try {
            const response = await fetch(url, config);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    // Galaxy API methods
    async createGalaxy(galaxyData) {
        return this.request('/galaxies', {
            method: 'POST',
            body: JSON.stringify(galaxyData)
        });
    }

    async getGalaxy(id) {
        return this.request(`/galaxies/${id}`);
    }

    async updateGalaxy(id, galaxyData) {
        return this.request(`/galaxies/${id}`, {
            method: 'PUT',
            body: JSON.stringify(galaxyData)
        });
    }

    async deleteGalaxy(id) {
        return this.request(`/galaxies/${id}`, {
            method: 'DELETE'
        });
    }

    // Image upload methods
    async uploadImages(files) {
        const formData = new FormData();
        
        for (let i = 0; i < files.length; i++) {
            formData.append('images[]', files[i]);
        }

        return this.request('/images/upload', {
            method: 'POST',
            headers: {
                // Don't set Content-Type for FormData, let browser set it
            },
            body: formData
        });
    }

    async deleteImage(imagePath) {
        return this.request('/images', {
            method: 'DELETE',
            body: JSON.stringify({ image_path: imagePath })
        });
    }
}

// Create API client instance
const apiClient = new ApiClient(API_BASE_URL);

// Export functions that match the Firebase interface for easy migration
export const db = {
    // This object is kept for compatibility but will use our API
};

export const collection = (collectionName) => {
    return {
        name: collectionName
    };
};

export const addDoc = async (collectionRef, data) => {
    if (collectionRef.name === 'galaxies') {
        const response = await apiClient.createGalaxy(data);
        return {
            id: response.data.id
        };
    }
    throw new Error('Collection not supported');
};

export const doc = (db, collectionName, docId) => {
    return {
        collection: collectionName,
        id: docId
    };
};

export const getDoc = async (docRef) => {
    if (docRef.collection === 'galaxies') {
        try {
            const response = await apiClient.getGalaxy(docRef.id);
            return {
                exists: () => true,
                data: () => response.data
            };
        } catch (error) {
            return {
                exists: () => false,
                data: () => null
            };
        }
    }
    throw new Error('Collection not supported');
};

// Storage functions
export const storage = {
    // Placeholder for storage object
};

export const ref = (storage, path) => {
    return {
        path: path
    };
};

export const uploadBytes = async (storageRef, file) => {
    // This will be handled by the image upload API
    return {
        ref: storageRef
    };
};

export const getDownloadURL = async (storageRef) => {
    // This will be handled by the image upload API
    return storageRef.path;
};

// Export the API client for direct use
export { apiClient };
